"""
Persistent Step Data Storage for GretahAI ScriptWeaver

This module provides persistent JSON-based storage for test case step data,
ensuring hybrid-edited steps remain the authoritative source across all stages
and application restarts.

Key Features:
- JSON file-based persistence for step tables
- Automatic synchronization with hybrid editing operations
- Data consistency validation and conflict resolution
- Comprehensive logging for debugging data flow issues
- Thread-safe file operations with proper locking

File Naming Convention:
- step_data_{test_case_id}_{timestamp}.json
- Latest file for each test case is used as authoritative source
"""

import os
import json
import logging
import threading
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import tempfile

# Handle fcntl import for cross-platform compatibility
try:
    import fcntl
    HAS_FCNTL = True
except ImportError:
    # fcntl is not available on Windows
    HAS_FCNTL = False

# Set up logging
logger = logging.getLogger(__name__)

# Configuration
STEP_DATA_DIR = "step_data_storage"
MAX_BACKUP_FILES = 10  # Keep last 10 versions per test case
FILE_LOCK_TIMEOUT = 30.0  # seconds

# Thread-local storage for file locks
thread_local = threading.local()


class StepDataStorage:
    """
    Persistent storage manager for test case step data.

    Provides methods to save, retrieve, and manage step table data across
    application sessions using JSON files with proper versioning and validation.
    """

    def __init__(self, storage_dir: str = None):
        """
        Initialize the step data storage.

        Args:
            storage_dir: Directory for storing step data files. If None, uses default.
        """
        if storage_dir is None:
            # Use default directory in the application root
            app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            storage_dir = os.path.join(app_dir, STEP_DATA_DIR)

        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)

        logger.info(f"StepDataStorage initialized with directory: {self.storage_dir}")

    def _generate_filename(self, test_case_id: str, timestamp: datetime = None) -> str:
        """
        Generate a filename for step data storage.

        Args:
            test_case_id: ID of the test case
            timestamp: Timestamp for the file. If None, uses current time.

        Returns:
            str: Generated filename
        """
        if timestamp is None:
            timestamp = datetime.now()

        # Sanitize test case ID for filename
        safe_id = "".join(c for c in test_case_id if c.isalnum() or c in ('-', '_')).rstrip()
        # Use microseconds to avoid filename collisions
        timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S_%f")

        return f"step_data_{safe_id}_{timestamp_str}.json"

    def _get_latest_file(self, test_case_id: str) -> Optional[Path]:
        """
        Get the latest step data file for a test case.

        Args:
            test_case_id: ID of the test case

        Returns:
            Path to the latest file or None if not found
        """
        safe_id = "".join(c for c in test_case_id if c.isalnum() or c in ('-', '_')).rstrip()
        pattern = f"step_data_{safe_id}_*.json"

        matching_files = list(self.storage_dir.glob(pattern))
        if not matching_files:
            return None

        # Sort by modification time, newest first
        matching_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
        return matching_files[0]

    def _calculate_data_hash(self, step_data: List[Dict[str, Any]]) -> str:
        """
        Calculate a hash of the step data for integrity checking.

        Args:
            step_data: List of step dictionaries

        Returns:
            str: SHA256 hash of the data
        """
        # Create a normalized JSON string for hashing
        normalized_json = json.dumps(step_data, sort_keys=True, separators=(',', ':'))
        return hashlib.sha256(normalized_json.encode('utf-8')).hexdigest()

    def save_step_data(self, test_case_id: str, step_data: List[Dict[str, Any]],
                      metadata: Dict[str, Any] = None) -> bool:
        """
        Save step data to a JSON file with metadata.

        Args:
            test_case_id: ID of the test case
            step_data: List of step dictionaries to save
            metadata: Additional metadata about the step data

        Returns:
            bool: True if saved successfully, False otherwise
        """
        try:
            timestamp = datetime.now()
            filename = self._generate_filename(test_case_id, timestamp)
            file_path = self.storage_dir / filename

            # Prepare data structure
            data_to_save = {
                "test_case_id": test_case_id,
                "timestamp": timestamp.isoformat(),
                "data_hash": self._calculate_data_hash(step_data),
                "step_count": len(step_data),
                "metadata": metadata or {},
                "step_data": step_data
            }

            # Write to temporary file first, then rename for atomic operation
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json',
                                           dir=self.storage_dir, delete=False) as temp_file:
                json.dump(data_to_save, temp_file, indent=2, ensure_ascii=False)
                temp_path = temp_file.name

            # Atomic rename
            os.rename(temp_path, file_path)

            logger.info(f"Saved step data for test case {test_case_id}: {filename}")
            logger.info(f"  → Step count: {len(step_data)}")
            logger.info(f"  → Data hash: {data_to_save['data_hash'][:16]}...")

            # Clean up old files
            self._cleanup_old_files(test_case_id)

            return True

        except Exception as e:
            logger.error(f"Failed to save step data for test case {test_case_id}: {e}")
            return False

    def load_step_data(self, test_case_id: str) -> Optional[Tuple[List[Dict[str, Any]], Dict[str, Any]]]:
        """
        Load the latest step data for a test case.

        Args:
            test_case_id: ID of the test case

        Returns:
            Tuple of (step_data, metadata) or None if not found
        """
        try:
            latest_file = self._get_latest_file(test_case_id)
            if not latest_file:
                logger.info(f"No step data file found for test case: {test_case_id}")
                return None

            with open(latest_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Validate data structure
            required_fields = ['test_case_id', 'timestamp', 'step_data']
            for field in required_fields:
                if field not in data:
                    logger.error(f"Invalid step data file {latest_file}: missing field '{field}'")
                    return None

            step_data = data['step_data']
            metadata = data.get('metadata', {})

            # Validate data integrity if hash is available
            if 'data_hash' in data:
                calculated_hash = self._calculate_data_hash(step_data)
                stored_hash = data['data_hash']
                if calculated_hash != stored_hash:
                    logger.warning(f"Data integrity check failed for {latest_file}")
                    logger.warning(f"  → Stored hash: {stored_hash}")
                    logger.warning(f"  → Calculated hash: {calculated_hash}")
                    # Continue anyway, but log the issue

            logger.info(f"Loaded step data for test case {test_case_id}: {latest_file.name}")
            logger.info(f"  → Step count: {len(step_data)}")
            logger.info(f"  → File timestamp: {data.get('timestamp', 'unknown')}")

            return step_data, metadata

        except Exception as e:
            logger.error(f"Failed to load step data for test case {test_case_id}: {e}")
            return None

    def _cleanup_old_files(self, test_case_id: str):
        """
        Clean up old step data files, keeping only the most recent ones.

        Args:
            test_case_id: ID of the test case
        """
        try:
            safe_id = "".join(c for c in test_case_id if c.isalnum() or c in ('-', '_')).rstrip()
            pattern = f"step_data_{safe_id}_*.json"

            matching_files = list(self.storage_dir.glob(pattern))
            if len(matching_files) <= MAX_BACKUP_FILES:
                return

            # Sort by modification time, newest first
            matching_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)

            # Remove old files beyond the limit
            files_to_remove = matching_files[MAX_BACKUP_FILES:]
            for file_path in files_to_remove:
                try:
                    file_path.unlink()
                    logger.info(f"Cleaned up old step data file: {file_path.name}")
                except Exception as e:
                    logger.warning(f"Failed to remove old file {file_path}: {e}")

        except Exception as e:
            logger.error(f"Failed to cleanup old files for test case {test_case_id}: {e}")


# Global storage instance
_step_storage_instance = None
_storage_lock = threading.Lock()


def get_step_data_storage() -> StepDataStorage:
    """
    Get the global step data storage instance.

    Returns:
        StepDataStorage: The global storage instance
    """
    global _step_storage_instance

    if _step_storage_instance is None:
        with _storage_lock:
            if _step_storage_instance is None:
                _step_storage_instance = StepDataStorage()

    return _step_storage_instance
